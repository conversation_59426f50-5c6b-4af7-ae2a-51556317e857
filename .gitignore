# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
index.android.bundle
index.android.bundle.map

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Bundle artifact
*.jsbundle

# Generated obfuscated code
.jso/

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# General
.env
coverage/
junit.xml
.eslintcache

# Git secrets
.gitsecret/keys/random_seed
.gitsecret/keys/pubring.kbx~
.env.local
.env.dev
.env.test
.env.preprod
.env.*
!*.secret
android/app/gradle.properties

# Pipeline script outputs
dependencies.txt

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
# testing
/coverage

# yarn
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions