# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
index.android.bundle
index.android.bundle.map

# Android specific
android/app/build/
android/build/
android/.gradle/
android/app/.cxx/
android/app/src/main/assets/
android/app/src/main/res/drawable-*/
android/app/src/main/res/mipmap-*/
*.apk
*.aab
*.dex
*.class

# node.js
#
node_modules/
npm-debug.log
yarn-error.log
*.log
*.tmp
*.temp

# Package manager files
# Note: package-lock.json should usually be committed for consistency
# Uncomment the line below if you want to ignore it
# package-lock.json
yarn.lock
pnpm-lock.yaml
.pnpm-store/

# Cache directories
.npm/
.yarn/
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# BUCK
buck-out/
\.buckd/

# Generated obfuscated code
.jso/

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/
ios/Podfile.lock

# iOS specific
ios/build/
ios/*.xcworkspace/
ios/*.xcuserstate
ios/DerivedData/
*.dSYM.zip
*.dSYM

# General
.env
junit.xml

# Git secrets
.gitsecret/keys/random_seed
.gitsecret/keys/pubring.kbx~
.env.local
.env.dev
.env.test
.env.preprod
.env.*
!*.secret
android/app/gradle.properties

# Pipeline script outputs
dependencies.txt

# yarn
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# TypeScript
*.tsbuildinfo
*.tscache

# React Native specific
.metro-health-check*
metro.config.js.bak
*.map

# Flipper
.flipper/
ios/Pods/Flipper*
android/app/src/debug/java/com/*/ReactNativeFlipper.java

# Watchman
.watchmanconfig

# Expo (if used)
.expo/
web-build/

# Detox
.detoxrc.json
e2e/artifacts/

# Storybook
storybook-static/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.orig

# Build artifacts
dist/
lib/
out/

# Test coverage
coverage/
.nyc_output/

# Sentry
.sentryclirc

# Firebase
.firebaserc
firebase-debug.log
firestore-debug.log
ui-debug.log

# Gradle Wrapper (keep the jar)
!gradle/wrapper/gradle-wrapper.jar

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile

# Native build artifacts
*.so
*.dylib
*.dll
*.a
*.lib

# React Native codegen
react-native.config.js.bak

# Additional common patterns
*.orig
*.rej
.DS_Store?
Icon?
._*
.Spotlight-V100
.Trashes
ehthumbs_vista.db

# JetBrains IDEs
.idea/
*.iws
*.ipr

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# macOS
.AppleDouble
.LSOverride
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk