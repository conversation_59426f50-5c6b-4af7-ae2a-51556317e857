import React, { useState, useEffect } from 'react';
import { StatusBar } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';
import SplashScreen from './src/components/SplashScreen';
import AppNavigator from './src/navigation/AppNavigator';
import linking from './src/utils/linking';
import DeepLinkHandler from './src/utils/DeepLinkHandler';

function App(): React.JSX.Element {
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  if (showSplash) {
    return (
      <>
        <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />
        <SplashScreen onFinish={handleSplashFinish} />
      </>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />
      <NavigationContainer linking={linking}>
        <AppNavigator />
        {/* Global listener for advanced deep-link flows */}
        <DeepLinkHandler />
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}

export default App;
