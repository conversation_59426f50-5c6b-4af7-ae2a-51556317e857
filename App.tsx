import React, { useState } from "react";
import { StatusBar } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { NavigationContainer } from "@react-navigation/native";
import SplashScreen from "./src/components/SplashScreen";
import WelcomeScreen from "./src/screens/WelcomeScreen";
import LoginScreen from "./src/screens/LoginScreen";
import SignUpScreen from "./src/screens/SignUpScreen";
import AppNavigator from "./src/navigation/AppNavigator";
import linking from "./src/utils/linking";
import DeepLinkHandler from "./src/utils/DeepLinkHandler";

type AppState = "splash" | "welcome" | "login" | "signup" | "main";

function App(): React.JSX.Element {
  const [appState, setAppState] = useState<AppState>("splash");

  const handleSplashFinish = () => {
    setAppState("welcome");
  };

  const handleLogin = () => {
    setAppState("main");
  };

  const handleSignUp = () => {
    setAppState("main");
  };

  const handleBackToWelcome = () => {
    setAppState("welcome");
  };

  const handleForgotPassword = () => {
    // Handle forgot password navigation
    console.log("Navigate to forgot password");
  };

  if (appState === "splash") {
    return (
      <>
        <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />
        <SplashScreen onFinish={handleSplashFinish} />
      </>
    );
  }

  if (appState === "welcome") {
    return (
      <WelcomeScreen
        onLogin={() => setAppState("login")}
        onSignUp={() => setAppState("signup")}
      />
    );
  }

  if (appState === "login") {
    return (
      <LoginScreen
        onLogin={handleLogin}
        onBackToWelcome={handleBackToWelcome}
        onForgotPassword={handleForgotPassword}
        onSignUp={() => setAppState("signup")}
      />
    );
  }

  if (appState === "signup") {
    return (
      <SignUpScreen
        onSignUp={handleSignUp}
        onBackToWelcome={handleBackToWelcome}
        onLogin={() => setAppState("login")}
      />
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />
      <NavigationContainer linking={linking}>
        <AppNavigator />
        {/* Global listener for advanced deep-link flows */}
        <DeepLinkHandler />
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}

export default App;
