{"logs": [{"outputFile": "com.nurservnurseapp-mergeDebugResources-41:/values-ko/values-ko.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,917,979,1060,1122,1179,1266,1326,1384,1442,1501,1558,1612,1707,1763,1820,1874,1940,2044,2119,2191,2272,2350,2427,2548,2613,2678,2778,2857,2932,2982,3033,3099,3163,3233,3304,3375,3443,3514,3586,3656,3749,3829,3903,3983,4065,4137,4202,4274,4322,4395,4459,4534,4611,4673,4737,4800,4867,4951,5029,5109,5187,5241,5296,5368,5445,5518", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,516,595,678,784,859,912,974,1055,1117,1174,1261,1321,1379,1437,1496,1553,1607,1702,1758,1815,1869,1935,2039,2114,2186,2267,2345,2422,2543,2608,2673,2773,2852,2927,2977,3028,3094,3158,3228,3299,3370,3438,3509,3581,3651,3744,3824,3898,3978,4060,4132,4197,4269,4317,4390,4454,4529,4606,4668,4732,4795,4862,4946,5024,5104,5182,5236,5291,5363,5440,5513,5584"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2945,3010,3074,3143,3217,3973,4056,4162,4309,4362,4424,4572,4778,4900,4987,5047,5105,5163,5222,5279,5333,5428,5484,5541,5595,5661,5974,6049,6121,6202,6280,6357,6478,6543,6608,6708,6787,6862,6912,6963,7029,7093,7163,7234,7305,7373,7444,7516,7586,7679,7759,7833,7913,7995,8067,8132,8204,8252,8325,8389,8464,8541,8603,8667,8730,8797,8881,8959,9039,9117,9171,9226,9668,9745,9818", "endLines": "5,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "294,3005,3069,3138,3212,3291,4051,4157,4232,4357,4419,4500,4629,4830,4982,5042,5100,5158,5217,5274,5328,5423,5479,5536,5590,5656,5760,6044,6116,6197,6275,6352,6473,6538,6603,6703,6782,6857,6907,6958,7024,7088,7158,7229,7300,7368,7439,7511,7581,7674,7754,7828,7908,7990,8062,8127,8199,8247,8320,8384,8459,8536,8598,8662,8725,8792,8876,8954,9034,9112,9166,9221,9293,9740,9813,9884"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "39,40,41,42,43,44,45,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3296,3388,3488,3582,3679,3775,3873,10482", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3383,3483,3577,3674,3770,3868,3968,10578"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/577bab686e027aedbdda6a351ffbd6ab/transformed/appcompat-1.7.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,9589", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,9663"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,193,260,327,404,469,534,606,678,755,830,896,969,1043,1116,1193,1269,1341,1411,1480,1562,1630,1701,1768", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "116,188,255,322,399,464,529,601,673,750,825,891,964,1038,1111,1188,1264,1336,1406,1475,1557,1625,1696,1763,1835"}, "to": {"startLines": "33,49,53,55,56,58,72,73,74,121,122,123,124,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2879,4237,4505,4634,4701,4835,5765,5830,5902,9298,9375,9450,9516,9889,9963,10036,10113,10189,10261,10331,10400,10583,10651,10722,10789", "endColumns": "65,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "2940,4304,4567,4696,4773,4895,5825,5897,5969,9370,9445,9511,9584,9958,10031,10108,10184,10256,10326,10395,10477,10646,10717,10784,10856"}}]}]}