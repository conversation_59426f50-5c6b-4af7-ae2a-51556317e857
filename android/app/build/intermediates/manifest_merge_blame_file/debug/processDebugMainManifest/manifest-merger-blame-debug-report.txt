1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.nurservnurseapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:3:5-67
11-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:3:22-64
12    <!--
13    This manifest file is used only by <PERSON>radle to configure debug-only capabilities
14    for React Native Apps.
15    -->
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:5-78
16-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:22-75
17
18    <permission
18-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
19        android:name="com.nurservnurseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.nurservnurseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
23
24    <application
24-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
25        android:name="com.nurservnurseapp.MainApplication"
25-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:6:7-38
26        android:allowBackup="false"
26-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:10:7-34
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:icon="@mipmap/ic_launcher"
30-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:8:7-41
31        android:label="@string/app_name"
31-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:7:7-39
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:9:7-52
33        android:supportsRtl="true"
33-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:12:7-33
34        android:theme="@style/AppTheme"
34-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:11:7-38
35        android:usesCleartextTraffic="true" >
35-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:6:9-44
36        <activity
36-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:13:7-24:18
37            android:name="com.nurservnurseapp.MainActivity"
37-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:14:9-37
38            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
38-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:16:9-118
39            android:exported="true"
39-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:19:9-32
40            android:label="@string/app_name"
40-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:15:9-41
41            android:launchMode="singleTask"
41-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:17:9-40
42            android:windowSoftInputMode="adjustResize" >
42-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:18:9-51
43            <intent-filter>
43-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:20:9-23:25
44                <action android:name="android.intent.action.MAIN" />
44-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:21:13-65
44-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:21:21-62
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:22:13-73
46-->/Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:22:23-70
47            </intent-filter>
48        </activity>
49        <activity
49-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:19:9-21:40
50            android:name="com.facebook.react.devsupport.DevSettingsActivity"
50-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:20:13-77
51            android:exported="false" />
51-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:21:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
55            android:authorities="com.nurservnurseapp.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87
88        <meta-data
88-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
89            android:name="com.facebook.soloader.enabled"
89-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:13:13-57
90            android:value="false" />
90-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
91    </application>
92
93</manifest>
