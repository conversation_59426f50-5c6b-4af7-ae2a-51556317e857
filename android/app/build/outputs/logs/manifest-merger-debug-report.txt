-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:1:1-26:12
MERGED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:1:1-26:12
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-linear-gradient] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a20d4ea98db5280e93bbe097566c400f/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f9f5820a8fc51ef0881d52f5d314a8de/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7849ccba64cf4c69052a666f447426c6/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/577bab686e027aedbdda6a351ffbd6ab/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2ad1e845a384a470774aeac1a49c453/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ec1b8957df4de99938abc6d4058807a/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/69237cbc4aefa12209c329caf384c123/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d4e134d15479170abd019a155d8759b6/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3905300a45ca64d662b471375fd5e8d9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dfb5bbae7d5f16f599d5a165144d87b0/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7dd569041c0245c159d9be3447ca817a/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfe5bd7f18d96ed7d0cae6ce1f31cdbb/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f2b6f859df1dc59f1c7067d422a3cfb6/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fd553dfe93075a0db73e0416bef4638/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c148904a36cfaf5a46089ab230bf349e/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c15147ce40b8449784d249a201e6bbf3/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6c70e02c3dc44836a6c38024e072f4f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/20f763db476404005f26352892736044/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b9c81351c3622cd451ebc20e01ded460/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db181d901b05cea679f7c96dc38c1ae9/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/99f35c53378515c53167522e3b9a359b/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/272a780711e8126396d996730c781e1a/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/045a87ad5ecddd754e7718a3f19f4c58/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6d04985fb11e5f11faa8046bccb3e11/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3705b93634ecbc4990cb3aed22890e4f/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f042cb17fde40acab2c69aec2374b247/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1bee4b336b66ded81a0ef2c8572e2f19/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac91bdb7dcdba543ba78b5c778465f65/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2df22208e814961e362e82db0eb2f7cd/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e15d60b9e2ea40ccd7f59696a40d73c3/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/441db3ad6d268531ad84a27e9ecb0e88/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ee127c55d9d41ed09dfc140dd079abff/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/066aa26795d0c019644a2e29cdb6bd8c/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bdfeddf33e63bc2950acdb78dab02626/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67e3d6a6b06bb2cfe67b472bc2b16389/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43cd560e926a482a5ea1b3164e8bc36d/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/eeaffa684ad338e79d6770fbc0bac6c8/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6ecd6a89667c0b242853fa9401079ec/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2a5232374dd0530c3c9cbcab6deb17a4/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6627e730a92816e8c0dcc72e1e336a81/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/e5085d66e0bece9ceec518ea4c550b08/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ecdaf667fe1901e2d7e3c0e5c149861/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/551b5a49ee4a1352a91b38136084c0a9/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/7bb6509624a22505c9e53eb191018069/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e42702cbc33fccdcbcb8a8b0dc7f7dd/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/87dd86241f1fc82a38c7b64ccfb9b25b/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/068ed68cc385b0104f7c130eaa11c97a/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/fe5751599e93e2c5b008d598d4c1aee9/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd7f3f4be7d8b80c89a9e4d6df45a0bc/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e9ed9b2d1da091ea391f43a46838ad9a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c9d00d6ff77d8adac0f62fadcbe101ed/transformed/core-ktx-1.16.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/61f718de3a777c171665be6b33ec4b5e/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/97e300a49aaaf4c823b089e6856e82c5/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bcc5dc588b5b0518afa1f3edbffbb4be/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/31f81163ca6dffb93bf3c9ecd3786374/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f66c7b6b612a088df2e72d0c761c1749/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b215e34c4213bf64a7cc1cef35c1b0b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1492b7a7980fd76802c4bc2dd8a89b89/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/49f08ec2beac88b1b6d045e798e5b58f/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b93c4b01ba35a7bdccc65937517f5767/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a231b3d80ef7ff120aaa088ce546b741/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1f4c6336bcf1421c2f7124df895282dd/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/11b9f35a03b05761fb50bc0f7ed48be2/transformed/core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/266b592fadac20a4a95b0281e4be9127/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
	package
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:3:5-67
	android:name
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:3:22-64
application
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
MERGED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
MERGED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:5:5-25:19
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7849ccba64cf4c69052a666f447426c6/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7849ccba64cf4c69052a666f447426c6/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b215e34c4213bf64a7cc1cef35c1b0b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b215e34c4213bf64a7cc1cef35c1b0b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:12:7-33
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:12:7-33
	android:label
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:7:7-39
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:7:7-39
	tools:ignore
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:9:7-52
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:9:7-52
	tools:targetApi
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:7:9-29
	android:icon
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:8:7-41
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:8:7-41
	android:allowBackup
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:10:7-34
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:10:7-34
	android:theme
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:11:7-38
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:11:7-38
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml:6:9-44
	android:name
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:6:7-38
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:6:7-38
activity#com.nurservnurseapp.MainActivity
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:13:7-24:18
	android:label
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:15:9-41
	android:launchMode
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:17:9-40
	android:windowSoftInputMode
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:18:9-51
	android:exported
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:19:9-32
	android:configChanges
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:16:9-118
	android:name
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:14:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:20:9-23:25
action#android.intent.action.MAIN
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:21:13-65
	android:name
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:21:21-62
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:22:13-73
	android:name
		ADDED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/main/AndroidManifest.xml:22:23-70
uses-sdk
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-linear-gradient/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-reanimated/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-svg/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/projects/nurserv/NurServNurseApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a20d4ea98db5280e93bbe097566c400f/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a20d4ea98db5280e93bbe097566c400f/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/513a9d47ce658b0ab3152d50648d84c1/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f9f5820a8fc51ef0881d52f5d314a8de/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f9f5820a8fc51ef0881d52f5d314a8de/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7849ccba64cf4c69052a666f447426c6/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7849ccba64cf4c69052a666f447426c6/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/577bab686e027aedbdda6a351ffbd6ab/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/577bab686e027aedbdda6a351ffbd6ab/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2ad1e845a384a470774aeac1a49c453/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2ad1e845a384a470774aeac1a49c453/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ec1b8957df4de99938abc6d4058807a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1ec1b8957df4de99938abc6d4058807a/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/69237cbc4aefa12209c329caf384c123/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/69237cbc4aefa12209c329caf384c123/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d4e134d15479170abd019a155d8759b6/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d4e134d15479170abd019a155d8759b6/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3905300a45ca64d662b471375fd5e8d9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3905300a45ca64d662b471375fd5e8d9/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dfb5bbae7d5f16f599d5a165144d87b0/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/dfb5bbae7d5f16f599d5a165144d87b0/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7dd569041c0245c159d9be3447ca817a/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7dd569041c0245c159d9be3447ca817a/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfe5bd7f18d96ed7d0cae6ce1f31cdbb/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfe5bd7f18d96ed7d0cae6ce1f31cdbb/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f2b6f859df1dc59f1c7067d422a3cfb6/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f2b6f859df1dc59f1c7067d422a3cfb6/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fd553dfe93075a0db73e0416bef4638/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/9fd553dfe93075a0db73e0416bef4638/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c148904a36cfaf5a46089ab230bf349e/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c148904a36cfaf5a46089ab230bf349e/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c15147ce40b8449784d249a201e6bbf3/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c15147ce40b8449784d249a201e6bbf3/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6c70e02c3dc44836a6c38024e072f4f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6c70e02c3dc44836a6c38024e072f4f/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/20f763db476404005f26352892736044/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/20f763db476404005f26352892736044/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b9c81351c3622cd451ebc20e01ded460/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b9c81351c3622cd451ebc20e01ded460/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db181d901b05cea679f7c96dc38c1ae9/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db181d901b05cea679f7c96dc38c1ae9/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/99f35c53378515c53167522e3b9a359b/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/99f35c53378515c53167522e3b9a359b/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/272a780711e8126396d996730c781e1a/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/272a780711e8126396d996730c781e1a/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/045a87ad5ecddd754e7718a3f19f4c58/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/045a87ad5ecddd754e7718a3f19f4c58/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6d04985fb11e5f11faa8046bccb3e11/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6d04985fb11e5f11faa8046bccb3e11/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3705b93634ecbc4990cb3aed22890e4f/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3705b93634ecbc4990cb3aed22890e4f/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f042cb17fde40acab2c69aec2374b247/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f042cb17fde40acab2c69aec2374b247/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1bee4b336b66ded81a0ef2c8572e2f19/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1bee4b336b66ded81a0ef2c8572e2f19/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac91bdb7dcdba543ba78b5c778465f65/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ac91bdb7dcdba543ba78b5c778465f65/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2df22208e814961e362e82db0eb2f7cd/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2df22208e814961e362e82db0eb2f7cd/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e15d60b9e2ea40ccd7f59696a40d73c3/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e15d60b9e2ea40ccd7f59696a40d73c3/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/441db3ad6d268531ad84a27e9ecb0e88/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/441db3ad6d268531ad84a27e9ecb0e88/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ee127c55d9d41ed09dfc140dd079abff/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ee127c55d9d41ed09dfc140dd079abff/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/066aa26795d0c019644a2e29cdb6bd8c/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/066aa26795d0c019644a2e29cdb6bd8c/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bdfeddf33e63bc2950acdb78dab02626/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bdfeddf33e63bc2950acdb78dab02626/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67e3d6a6b06bb2cfe67b472bc2b16389/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/67e3d6a6b06bb2cfe67b472bc2b16389/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43cd560e926a482a5ea1b3164e8bc36d/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/43cd560e926a482a5ea1b3164e8bc36d/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/eeaffa684ad338e79d6770fbc0bac6c8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/eeaffa684ad338e79d6770fbc0bac6c8/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6ecd6a89667c0b242853fa9401079ec/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/d6ecd6a89667c0b242853fa9401079ec/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2a5232374dd0530c3c9cbcab6deb17a4/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2a5232374dd0530c3c9cbcab6deb17a4/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6627e730a92816e8c0dcc72e1e336a81/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6627e730a92816e8c0dcc72e1e336a81/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/e5085d66e0bece9ceec518ea4c550b08/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/e5085d66e0bece9ceec518ea4c550b08/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ecdaf667fe1901e2d7e3c0e5c149861/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/0ecdaf667fe1901e2d7e3c0e5c149861/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/551b5a49ee4a1352a91b38136084c0a9/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/551b5a49ee4a1352a91b38136084c0a9/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/7bb6509624a22505c9e53eb191018069/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/7bb6509624a22505c9e53eb191018069/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e42702cbc33fccdcbcb8a8b0dc7f7dd/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e42702cbc33fccdcbcb8a8b0dc7f7dd/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/87dd86241f1fc82a38c7b64ccfb9b25b/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/87dd86241f1fc82a38c7b64ccfb9b25b/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/068ed68cc385b0104f7c130eaa11c97a/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/068ed68cc385b0104f7c130eaa11c97a/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/fe5751599e93e2c5b008d598d4c1aee9/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/fe5751599e93e2c5b008d598d4c1aee9/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd7f3f4be7d8b80c89a9e4d6df45a0bc/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd7f3f4be7d8b80c89a9e4d6df45a0bc/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e9ed9b2d1da091ea391f43a46838ad9a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/e9ed9b2d1da091ea391f43a46838ad9a/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c9d00d6ff77d8adac0f62fadcbe101ed/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c9d00d6ff77d8adac0f62fadcbe101ed/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/61f718de3a777c171665be6b33ec4b5e/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/61f718de3a777c171665be6b33ec4b5e/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/97e300a49aaaf4c823b089e6856e82c5/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/97e300a49aaaf4c823b089e6856e82c5/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bcc5dc588b5b0518afa1f3edbffbb4be/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bcc5dc588b5b0518afa1f3edbffbb4be/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/31f81163ca6dffb93bf3c9ecd3786374/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/31f81163ca6dffb93bf3c9ecd3786374/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f66c7b6b612a088df2e72d0c761c1749/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f66c7b6b612a088df2e72d0c761c1749/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b215e34c4213bf64a7cc1cef35c1b0b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b215e34c4213bf64a7cc1cef35c1b0b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1492b7a7980fd76802c4bc2dd8a89b89/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1492b7a7980fd76802c4bc2dd8a89b89/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/49f08ec2beac88b1b6d045e798e5b58f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/49f08ec2beac88b1b6d045e798e5b58f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b93c4b01ba35a7bdccc65937517f5767/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b93c4b01ba35a7bdccc65937517f5767/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a231b3d80ef7ff120aaa088ce546b741/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a231b3d80ef7ff120aaa088ce546b741/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1f4c6336bcf1421c2f7124df895282dd/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1f4c6336bcf1421c2f7124df895282dd/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/11b9f35a03b05761fb50bc0f7ed48be2/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/11b9f35a03b05761fb50bc0f7ed48be2/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/266b592fadac20a4a95b0281e4be9127/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/266b592fadac20a4a95b0281e4be9127/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/projects/nurserv/NurServNurseApp/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/dce0868bdc6f6509eb4c34e67c463f19/transformed/react-android-0.80.1-debug/AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/db9b89a0bd3e24a1c34dca7f547c4742/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/845972de740b8ae1f7e70b3571ff7e9a/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
permission#com.nurservnurseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
uses-permission#com.nurservnurseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f6ffc7e379b3178929ea5e334efee649/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/12dd556d44ed564833187061d67aabe9/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/4202c0979ba32d0c2d2d3859867bb6e8/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab05c7d64886b4cb72262bf1ff4d9932/transformed/soloader-0.12.1/AndroidManifest.xml:13:13-57
