# Deep-Link Integration Guide  
_NurServNurseApp_

This document walks you through everything required to add, configure, and verify deep-link support in **NurServNurseApp** for both Android and iOS.  
When finished you will be able to open the app directly to any feature screen using URLs such as:

```
nurservnurse://services/123
https://nurservnurse.com/appointments/abc
```

---

## 1. How Deep Links Work in this Project

1. **URL prefixes** and **path → screen** mappings live in  
   `src/utils/linking.ts`.
2. React Navigation receives the `linking` object from that file inside `NavigationContainer`.
3. A lightweight component `src/utils/DeepLinkHandler.tsx` listens for URLs and manually routes advanced cases (e.g. nested params, push notifications).
4. Platform code (Android Manifest & iOS Info.plist) advertises the custom scheme `nurservnurse://` and optional universal-/app-link domains `https://nurservnurse.com`.

> Important: Every new screen you want to expose externally **must** be added in two places  
> • `DEEP_LINK_PATHS` in `linking.ts`  
> • The `config.screens` map inside the same file

---

## 2. Adding the Linking Prop in `App.tsx`

```tsx
import { NavigationContainer } from '@react-navigation/native';
import linking from './src/utils/linking';

export default function App() {
  return (
    <NavigationContainer linking={linking}>
      {/* your navigators */}
      <DeepLinkHandler />
    </NavigationContainer>
  );
}
```

---

## 3. Android Configuration

### 3.1. Declare the custom scheme

Edit `android/app/src/main/AndroidManifest.xml` inside the `<application>` tag:

```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT"/>
    <category android:name="android.intent.category.BROWSABLE"/>

    <!-- Custom scheme -->
    <data android:scheme="nurservnurse"/>
    <!-- Optional: HTTP(S) App Links -->
    <data android:scheme="https"
          android:host="nurservnurse.com"/>
    <data android:scheme="https"
          android:host="www.nurservnurse.com"/>
</intent-filter>
```

`android:autoVerify="true"` triggers Play Services to verify the association file (see next section).

### 3.2. Enable App Links (optional but recommended)

1. At `https://nurservnurse.com/.well-known/assetlinks.json` publish:

```json
[{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target": {
    "namespace": "android_app",
    "package_name": "com.nurservnurseapp",
    "sha256_cert_fingerprints": ["<release SHA-256 fingerprint>"]
  }
}]
```

2. Ensure your **release** keystore fingerprint is used. You can list it with:

```
keytool -list -v -keystore release.keystore
```

### 3.3. Rebuild & Test

```
yarn android
adb shell am start -W -a android.intent.action.VIEW -d "nurservnurse://home"
adb shell am start -W -a android.intent.action.VIEW -d "https://nurservnurse.com/appointments/xyz"
```

---

## 4. iOS Configuration

### 4.1. Add URL scheme

Open **Xcode > Info** or edit `ios/NurServNurseApp/Info.plist`:

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>nurservnurse</string>
    </array>
  </dict>
</array>
```

### 4.2. Add Universal Links (Associated Domains)

1. In Xcode target settings **Signing & Capabilities** ➜ **+ Capability** ➜ _Associated Domains_.
2. Add:

```
applinks:nurservnurse.com
applinks:www.nurservnurse.com
```

3. On the web server create `.well-known/apple-app-site-association` (no extension, JSON content):

```json
{
  "applinks": {
    "apps": [],
    "details": [{
      "appID": "<TEAM_ID>.com.nurservnurseapp",
      "paths": ["/", "/*"]
    }]
  }
}
```

Re-sign & rebuild:

```
cd ios && pod install
yarn ios
```

### 4.3. Test

In Safari (simulator or device) enter:

```
nurservnurse://services
https://nurservnurse.com/login
```

For universal links hold **⌘** while clicking to force open in the app.

---

## 5. Creating Links Programmatically

Use the helper in `linking.ts`:

```ts
import { createDeepLink } from '../utils/linking';

const url = createDeepLink('services/123', { ref: 'promo' });
// nurservnurse://services/123?ref=promo
```

You can embed this URL in an email, push notification, or clipboard.

---

## 6. Adding a New Screen to Deep-Link Map

1. Add a constant in `DEEP_LINK_PATHS`:

```ts
NEW_FEATURE: 'new-feature'
```

2. Extend `config.screens`:

```ts
NewFeature: 'new-feature',
```

3. (Optional) Handle complex param logic inside `DeepLinkHandler.tsx`.

4. Update navigation type definitions in `src/types/navigation.ts`.

---

## 7. Troubleshooting

| Symptom | Likely Cause | Fix |
|---------|--------------|-----|
| App opens but lands on wrong screen | Path not mapped or typo in `DEEP_LINK_PATHS` | Verify path + `config.screens` |
| Android opens browser not app | SHA-256 mismatch or missing intent-filter | Re-generate assetlinks.json, clear Play Services cache |
| iOS opens Safari | Associated Domains not added or server file incorrect | Check `apple-app-site-association` validity using `curl -I` |
| Nothing happens while app in background | `Linking.addEventListener` removed accidentally | Ensure `DeepLinkHandler` is mounted under `NavigationContainer` |

---

## 8. References

* React Navigation Deep Linking docs  
  https://reactnavigation.org/docs/deep-linking
* Android App Links docs  
  https://developer.android.com/training/app-links
* Apple Universal Links docs  
  https://developer.apple.com/library/archive/documentation/General/Conceptual/AppSearch/UniversalLinks.html

---

You are now ready to ship feature-rich, shareable links that drive users straight to the right place in NurServNurseApp. 🎉
