# Navigation System – NurServNurseApp

This document gives an overview of **how screens are organised and routed** inside the app and shows **where deep-link support is wired in**.

---

## 1. Directory Layout

```
src/
├── navigation/
│   ├── AppNavigator.tsx    ← Bottom-tab navigator (UI only)
│   └── README.md           ← you are here
├── utils/
│   ├── linking.ts          ← React-Navigation linking object
│   └── DeepLinkHandler.tsx ← Listener for advanced flows
└── types/
    └── navigation.ts       ← Type-safe param lists
```

* `AppNavigator.tsx` hosts the **visual** navigator (tabs, icons, headers).  
* The **NavigationContainer** lives one level up in `App.tsx` because it needs global providers (gesture-handler, status bar) and receives the `linking` prop.
* TypeScript helpers in `src/types/navigation.ts` keep all route params strongly typed.

---

## 2. High-Level Flow

1. `App.tsx`
   * Wraps everything inside `NavigationContainer`  
   * Injects the `linking` object (deep-link map)  
   * Mounts `DeepLinkHandler` to catch incoming URLs while the app is open
2. `AppNavigator.tsx`
   * Creates the **BottomTabNavigator** with two root tabs:
     * `Home`
     * `Services`
   * Can be extended with nested **stack** or **drawer** navigators when new features arrive.
3. Screens render inside the selected tab.

---

## 3. Adding a New Screen

1. Create the screen component in `src/screens/`.
2. Add it to the appropriate navigator inside `AppNavigator.tsx` (or create a new nested navigator).
3. Update the **types** in `src/types/navigation.ts`.
4. (Optional) Expose the screen to deep linking – see next section.

---

## 4. Deep-Link Integration

Deep linking is a two-part story:

### 4.1 Static Map (`linking.ts`)

```ts
export const linking = {
  prefixes: ['nurservnurse://', 'https://nurservnurse.com'],
  config: {
    screens: {
      Home: 'home',
      Services: {
        path: 'services',
        screens: {
          ServiceDetails: 'services/:serviceId',
        },
      },
      /* …other screens… */
    },
  },
};
```

React Navigation automatically matches an incoming URL to the correct route.

### 4.2 Dynamic Handler (`DeepLinkHandler.tsx`)

Some links need extra logic (e.g. push notification payloads, analytics params).  
`DeepLinkHandler`:

* Listens to `Linking` events
* Normalises/validates the URL
* Manually calls `navigation.navigate(...)` for complex cases

The component is mounted once under `NavigationContainer`, has **no UI**, and cleans up its listeners on unmount.

### 4.3 Platform Glue

* **Android** – intent-filter in `android/app/src/main/AndroidManifest.xml`
* **iOS** – URL Schemes (Info.plist) and Associated Domains (Xcode capability)

See `docs/DeepLinkSetup.md` for full instructions.

---

## 5. Navigation Best Practices

* Keep navigators small and focused; nest when features grow.
* Centralise route names/params in `src/types/navigation.ts` to avoid typos.
* Prefer **stack** navigators for flows (e.g. checkout), **tabs** for top-level sections.
* When adding a new deep link:
  1. Declare a constant in `DEEP_LINK_PATHS` inside `linking.ts`.
  2. Map the path under `config.screens`.
  3. Handle edge-cases (if any) in `DeepLinkHandler.tsx`.

---

Happy routing! 🚀
