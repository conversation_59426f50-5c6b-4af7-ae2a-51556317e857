import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/Ionicons';

const { width, height } = Dimensions.get('window');

interface WelcomeScreenProps {
  onLogin: () => void;
  onSignUp: () => void;
}

export default function WelcomeScreen({ onLogin, onSignUp }: WelcomeScreenProps) {
  // Medical icons for background pattern
  const medicalIcons = [
    'medical', 'heart', 'pulse', 'thermometer', 'bandage',
    'medical-outline', 'heart-outline', 'pulse-outline', 'thermometer-outline',
    'fitness', 'fitness-outline', 'shield-checkmark', 'shield-checkmark-outline',
    'clipboard', 'clipboard-outline', 'time', 'time-outline'
  ];

  const renderBackgroundIcons = () => {
    const icons = [];
    const rows = 8;
    const cols = 6;
    
    for (let i = 0; i < rows * cols; i++) {
      const iconName = medicalIcons[i % medicalIcons.length];
      const row = Math.floor(i / cols);
      const col = i % cols;
      
      icons.push(
        <Icon
          key={i}
          name={iconName}
          size={24}
          color="rgba(255, 255, 255, 0.1)"
          style={[
            styles.backgroundIcon,
            {
              top: (row * height) / rows,
              left: (col * width) / cols,
            }
          ]}
        />
      );
    }
    return icons;
  };

  const renderNurseIllustration = () => {
    return (
      <View style={styles.nurseContainer}>
        {/* Left Nurse */}
        <View style={[styles.nurse, styles.leftNurse]}>
          <View style={styles.nurseHead}>
            <View style={styles.nurseHair} />
            <View style={styles.nurseFace} />
            <View style={styles.nurseMask} />
            <View style={styles.nurseHat} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: '#E8F4F8' }]}>
            <View style={styles.clipboard} />
          </View>
        </View>

        {/* Center Nurse */}
        <View style={[styles.nurse, styles.centerNurse]}>
          <View style={styles.nurseHead}>
            <View style={[styles.nurseHair, { backgroundColor: '#8B4513' }]} />
            <View style={[styles.nurseFace, { backgroundColor: '#D2691E' }]} />
            <View style={styles.nurseMask} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: '#4A90A4' }]}>
            <View style={styles.stethoscope} />
          </View>
        </View>

        {/* Right Nurse */}
        <View style={[styles.nurse, styles.rightNurse]}>
          <View style={styles.nurseHead}>
            <View style={[styles.nurseHair, { backgroundColor: '#654321' }]} />
            <View style={styles.nurseFace} />
            <View style={styles.nurseMask} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: '#E8F4F8' }]} />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />
      
      {/* Background with medical icons pattern */}
      <LinearGradient
        colors={['#1BA3C7', '#0891B2', '#0E7490']}
        style={styles.background}
      >
        {renderBackgroundIcons()}
        
        {/* Main Content */}
        <View style={styles.content}>
          {/* Logo/Brand */}
          <View style={styles.brandContainer}>
            <Text style={styles.brandText}>Nurserv</Text>
          </View>

          {/* Nurse Illustration */}
          {renderNurseIllustration()}
        </View>

        {/* Bottom Card */}
        <View style={styles.bottomCard}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Register as a Nearby Nurse</Text>
            <Text style={styles.cardSubtitle}>
              Flexible work hours, verified{'\n'}opportunities, and secure payments.
            </Text>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={styles.loginButton}
                onPress={onLogin}
                activeOpacity={0.8}
              >
                <Text style={styles.loginButtonText}>Login</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.signUpButton}
                onPress={onSignUp}
                activeOpacity={0.8}
              >
                <Text style={styles.signUpButtonText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    position: 'relative',
  },
  backgroundIcon: {
    position: 'absolute',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 60,
  },
  brandContainer: {
    marginBottom: 40,
  },
  brandText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    letterSpacing: 1,
  },
  nurseContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginBottom: 60,
  },
  nurse: {
    alignItems: 'center',
    marginHorizontal: 8,
  },
  leftNurse: {
    marginTop: 20,
  },
  centerNurse: {
    marginTop: 0,
    transform: [{ scale: 1.1 }],
  },
  rightNurse: {
    marginTop: 20,
  },
  nurseHead: {
    width: 50,
    height: 50,
    position: 'relative',
    marginBottom: 5,
  },
  nurseHair: {
    position: 'absolute',
    top: 0,
    left: 5,
    width: 40,
    height: 25,
    backgroundColor: '#4A4A4A',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  nurseFace: {
    position: 'absolute',
    top: 15,
    left: 10,
    width: 30,
    height: 25,
    backgroundColor: '#FDBCB4',
    borderRadius: 15,
  },
  nurseMask: {
    position: 'absolute',
    top: 25,
    left: 8,
    width: 34,
    height: 15,
    backgroundColor: '#E0E7FF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7D2FE',
  },
  nurseHat: {
    position: 'absolute',
    top: 5,
    left: 20,
    width: 10,
    height: 8,
    backgroundColor: 'white',
    borderRadius: 2,
  },
  nurseBody: {
    width: 60,
    height: 80,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  clipboard: {
    width: 20,
    height: 25,
    backgroundColor: 'white',
    borderRadius: 2,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  stethoscope: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 3,
    borderColor: '#374151',
    backgroundColor: 'transparent',
  },
  bottomCard: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardContent: {
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  cardSubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    width: '100%',
    gap: 16,
  },
  loginButton: {
    backgroundColor: '#1BA3C7',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#1BA3C7',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  signUpButton: {
    backgroundColor: 'transparent',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#1BA3C7',
  },
  signUpButtonText: {
    color: '#1BA3C7',
    fontSize: 18,
    fontWeight: '600',
  },
});
