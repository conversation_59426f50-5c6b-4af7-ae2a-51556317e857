import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Image,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import Icon from "react-native-vector-icons/Ionicons";

const { width, height } = Dimensions.get("window");

interface WelcomeScreenProps {
  onLogin: () => void;
  onSignUp: () => void;
}

export default function WelcomeScreen({
  onLogin,
  onSignUp,
}: WelcomeScreenProps) {
  // Medical icons for background pattern
  const medicalIcons = [
    "medical",
    "heart",
    "pulse",
    "thermometer",
    "bandage",
    "medical-outline",
    "heart-outline",
    "pulse-outline",
    "thermometer-outline",
    "fitness",
    "fitness-outline",
    "shield-checkmark",
    "shield-checkmark-outline",
    "clipboard",
    "clipboard-outline",
    "time",
    "time-outline",
  ];

  const renderBackgroundIcons = () => {
    const icons = [];
    const rows = 8;
    const cols = 6;

    for (let i = 0; i < rows * cols; i++) {
      const iconName = medicalIcons[i % medicalIcons.length];
      const row = Math.floor(i / cols);
      const col = i % cols;

      icons.push(
        <Icon
          key={i}
          name={iconName}
          size={24}
          color="rgba(255, 255, 255, 0.1)"
          style={[
            styles.backgroundIcon,
            {
              top: (row * height) / rows,
              left: (col * width) / cols,
            },
          ]}
        />
      );
    }
    return icons;
  };

  const renderNurseIllustration = () => {
    return (
      <View style={styles.nurseContainer}>
        {/* Left Nurse - Female with dark hair */}
        <View style={[styles.nurse, styles.leftNurse]}>
          <View style={styles.nurseHead}>
            <View style={[styles.nurseHair, { backgroundColor: "#1A1A2E" }]} />
            <View style={[styles.nurseFace, { backgroundColor: "#FDBCB4" }]} />
            <View style={styles.nurseMask} />
            <View style={styles.nurseEyes} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: "#7DD3C0" }]}>
            <View style={styles.clipboard} />
            <View style={styles.nurseArms} />
            <View style={styles.nursePocket} />
          </View>
        </View>

        {/* Center Nurse - Male with darker skin */}
        <View style={[styles.nurse, styles.centerNurse]}>
          <View style={styles.nurseHead}>
            <View style={[styles.nurseHair, { backgroundColor: "#2C1810" }]} />
            <View style={[styles.nurseFace, { backgroundColor: "#D2691E" }]} />
            <View style={styles.nurseMask} />
            <View style={styles.nurseEyes} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: "#7DD3C0" }]}>
            <View style={styles.stethoscope} />
            <View style={styles.nurseArms} />
            <View style={styles.nursePocket} />
          </View>
        </View>

        {/* Right Nurse - Male with light skin */}
        <View style={[styles.nurse, styles.rightNurse]}>
          <View style={styles.nurseHead}>
            <View style={[styles.nurseHair, { backgroundColor: "#8B4513" }]} />
            <View style={[styles.nurseFace, { backgroundColor: "#FDBCB4" }]} />
            <View style={styles.nurseMask} />
            <View style={styles.nurseEyes} />
          </View>
          <View style={[styles.nurseBody, { backgroundColor: "#7DD3C0" }]}>
            <View style={styles.tablet} />
            <View style={styles.nurseArms} />
            <View style={styles.nursePocket} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />

      {/* Background with medical icons pattern */}
      <LinearGradient
        colors={["#1BA3C7", "#0891B2", "#0E7490"]}
        style={styles.background}
      >
        {renderBackgroundIcons()}

        {/* Main Content */}
        <View style={styles.content}>
          {/* Logo/Brand */}
          <View style={styles.brandContainer}>
            <Image
              source={require("../assets/images/Logo.svg")}
              style={styles.logoImage}
              resizeMode="contain"
            />
            <Text style={styles.brandText}>Nurserv</Text>
          </View>

          {/* Nurse Illustration */}
          {renderNurseIllustration()}
        </View>

        {/* Bottom Card */}
        <View style={styles.bottomCard}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Register as a Nearby Nurse</Text>
            <Text style={styles.cardSubtitle}>
              Flexible work hours, verified{"\n"}opportunities, and secure
              payments.
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.loginButton}
                onPress={onLogin}
                activeOpacity={0.8}
              >
                <Text style={styles.loginButtonText}>Login</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.signUpButton}
                onPress={onSignUp}
                activeOpacity={0.8}
              >
                <Text style={styles.signUpButtonText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    position: "relative",
  },
  backgroundIcon: {
    position: "absolute",
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 60,
  },
  brandContainer: {
    marginBottom: 40,
    alignItems: "center",
  },
  logoImage: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  brandText: {
    fontSize: 36,
    fontWeight: "bold",
    color: "white",
    letterSpacing: 1,
  },
  nurseContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "center",
    marginBottom: 60,
  },
  nurse: {
    alignItems: "center",
    marginHorizontal: 8,
  },
  leftNurse: {
    marginTop: 20,
  },
  centerNurse: {
    marginTop: 0,
    transform: [{ scale: 1.1 }],
  },
  rightNurse: {
    marginTop: 20,
  },
  nurseHead: {
    width: 60,
    height: 60,
    position: "relative",
    marginBottom: 8,
  },
  nurseHair: {
    position: "absolute",
    top: 0,
    left: 6,
    width: 48,
    height: 30,
    backgroundColor: "#4A4A4A",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  nurseFace: {
    position: "absolute",
    top: 18,
    left: 12,
    width: 36,
    height: 30,
    backgroundColor: "#FDBCB4",
    borderRadius: 18,
  },
  nurseMask: {
    position: "absolute",
    top: 32,
    left: 10,
    width: 40,
    height: 18,
    backgroundColor: "#B8E6E1",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#7DD3C0",
  },
  nurseHat: {
    position: "absolute",
    top: 5,
    left: 20,
    width: 10,
    height: 8,
    backgroundColor: "white",
    borderRadius: 2,
  },
  nurseBody: {
    width: 70,
    height: 90,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  clipboard: {
    width: 20,
    height: 25,
    backgroundColor: "white",
    borderRadius: 2,
    borderWidth: 1,
    borderColor: "#D1D5DB",
  },
  stethoscope: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderWidth: 3,
    borderColor: "#374151",
    backgroundColor: "transparent",
  },
  nurseEyes: {
    position: "absolute",
    top: 24,
    left: 15,
    width: 30,
    height: 10,
    backgroundColor: "transparent",
  },
  nurseArms: {
    position: "absolute",
    bottom: 15,
    left: -8,
    right: -8,
    height: 25,
    backgroundColor: "#7DD3C0",
    borderRadius: 12,
  },
  nursePocket: {
    position: "absolute",
    top: 20,
    left: 18,
    width: 34,
    height: 10,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 5,
  },
  tablet: {
    width: 22,
    height: 28,
    backgroundColor: "#374151",
    borderRadius: 3,
    borderWidth: 1,
    borderColor: "#1F2937",
  },
  bottomCard: {
    backgroundColor: "white",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardContent: {
    alignItems: "center",
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 12,
    textAlign: "center",
  },
  cardSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    width: "100%",
    gap: 16,
  },
  loginButton: {
    backgroundColor: "#1BA3C7",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#1BA3C7",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "600",
  },
  signUpButton: {
    backgroundColor: "transparent",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#1BA3C7",
  },
  signUpButtonText: {
    color: "#1BA3C7",
    fontSize: 18,
    fontWeight: "600",
  },
});
