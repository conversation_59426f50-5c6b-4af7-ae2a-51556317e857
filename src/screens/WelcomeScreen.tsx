import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import LogoSvg from "../assets/images/Logo.svg";
import NurseIllustrationSvg from "../assets/images/Group 427318250.svg";

const { width, height } = Dimensions.get("window");

interface WelcomeScreenProps {
  onLogin: () => void;
  onSignUp: () => void;
}

export default function WelcomeScreen({
  onLogin,
  onSignUp,
}: WelcomeScreenProps) {
  const renderBackgroundPattern = () => {
    const patterns = [];
    const rows = 8;
    const cols = 6;

    for (let i = 0; i < rows * cols; i++) {
      const row = Math.floor(i / cols);
      const col = i % cols;

      patterns.push(
        <View
          key={i}
          style={[
            styles.backgroundPattern,
            {
              top: (row * height) / rows,
              left: (col * width) / cols,
            },
          ]}
        />
      );
    }
    return patterns;
  };

  const renderNurseIllustration = () => {
    return (
      <View style={styles.nurseContainer}>
        <NurseIllustrationSvg width={400} height={400} />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1BA3C7" />

      {/* Background with medical icons pattern */}
      <LinearGradient
        colors={["#1BA3C7", "#0891B2", "#0E7490"]}
        style={styles.background}
      >
        {renderBackgroundPattern()}

        {/* Main Content */}
        <View style={styles.content}>
          {/* Logo/Brand */}
          <View style={styles.brandContainer}>
            <LogoSvg width={150} height={100} />
          </View>

          {/* Nurse Illustration */}
          {renderNurseIllustration()}
        </View>

        {/* Bottom Card */}
        <View style={styles.bottomCard}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Register as a Nearby Nurse</Text>
            <Text style={styles.cardSubtitle}>
              Flexible work hours, verified{"\n"}opportunities, and secure
              payments.
            </Text>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.loginButton}
                onPress={onLogin}
                activeOpacity={0.8}
              >
                <Text style={styles.loginButtonText}>Login</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.signUpButton}
                onPress={onSignUp}
                activeOpacity={0.8}
              >
                <Text style={styles.signUpButtonText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    position: "relative",
  },
  backgroundIcon: {
    position: "absolute",
  },
  backgroundPattern: {
    position: "absolute",
    width: 20,
    height: 20,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 10,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 60,
  },
  brandContainer: {
    marginBottom: 40,
    alignItems: "center",
  },
  logoImage: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  brandText: {
    fontSize: 36,
    fontWeight: "bold",
    color: "white",
    letterSpacing: 1,
  },
  nurseContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 60,
  },

  bottomCard: {
    backgroundColor: "white",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 40,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardContent: {
    alignItems: "center",
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
    marginBottom: 12,
    textAlign: "center",
  },
  cardSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    width: "100%",
    gap: 16,
  },
  loginButton: {
    backgroundColor: "#1BA3C7",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    shadowColor: "#1BA3C7",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "600",
  },
  signUpButton: {
    backgroundColor: "transparent",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#1BA3C7",
  },
  signUpButtonText: {
    color: "#1BA3C7",
    fontSize: 18,
    fontWeight: "600",
  },
});
