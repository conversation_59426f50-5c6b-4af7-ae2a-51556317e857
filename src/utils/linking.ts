import { LinkingOptions } from '@react-navigation/native';
import { Linking } from 'react-native';

/**
 * URL prefixes for deep linking
 */
export const URL_SCHEME = 'nurservnurse://';
export const WEB_URL_PROD = 'https://nurservnurse.com';
export const WEB_URL_DEV = 'https://dev.nurservnurse.com';

/**
 * Get the appropriate URL prefixes based on environment
 */
export const getPrefixes = (): string[] => {
  const isProd = process.env.NODE_ENV === 'production';

  return [
    URL_SCHEME,
    isProd ? WEB_URL_PROD : WEB_URL_DEV,
    isProd ? `${WEB_URL_PROD}/*` : `${WEB_URL_DEV}/*`,
    'https://www.nurservnurse.com',
    'https://www.nurservnurse.com/*',
  ];
};

/**
 * Screen path configuration for deep linking
 */
export const DEEP_LINK_PATHS = {
  HOME: 'home',
  SERVICES: 'services',
  SERVICE_DETAILS: 'services/:serviceId',
  PROFILE: 'profile',
  APPOINTMENTS: 'appointments',
  APPOINTMENT_DETAILS: 'appointments/:appointmentId',
  NOTIFICATIONS: 'notifications',
  LOGIN: 'login',
  REGISTER: 'register',
  FORGOT_PASSWORD: 'forgot-password',
  EMERGENCY: 'emergency',
  SCHEDULE_VISIT: 'schedule',
};

/**
 * React Navigation linking configuration
 */
export const linking: LinkingOptions<ReactNavigation.RootParamList> = {
  prefixes: getPrefixes(),
  config: {
    initialRouteName: 'Home',
    screens: {
      Home: DEEP_LINK_PATHS.HOME,
      Services: {
        path: DEEP_LINK_PATHS.SERVICES,
        screens: {
          ServiceDetails: DEEP_LINK_PATHS.SERVICE_DETAILS,
        },
      },
      Profile: DEEP_LINK_PATHS.PROFILE,
      Appointments: {
        path: DEEP_LINK_PATHS.APPOINTMENTS,
        screens: {
          AppointmentDetails: DEEP_LINK_PATHS.APPOINTMENT_DETAILS,
        },
      },
      Notifications: DEEP_LINK_PATHS.NOTIFICATIONS,
      Login: DEEP_LINK_PATHS.LOGIN,
      Register: DEEP_LINK_PATHS.REGISTER,
      ForgotPassword: DEEP_LINK_PATHS.FORGOT_PASSWORD,
      Emergency: DEEP_LINK_PATHS.EMERGENCY,
      ScheduleVisit: DEEP_LINK_PATHS.SCHEDULE_VISIT,
    },
  },
  // Enable debugging in development
  async getInitialURL() {
    // First, you might want to handle any custom logic for URL handling
    // For example, if your app was opened from a notification

    // Get the deep link used to open the app
    const url = await Linking.getInitialURL();

    if (url != null) {
      console.log('App opened with URL:', url);
    }

    return url;
  },
  subscribe(listener) {
    // Listen to incoming links when the app is open
    const linkingSubscription = Linking.addEventListener('url', ({ url }) => {
      console.log('Incoming link:', url);
      listener(url);
    });

    return () => {
      // Clean up the event listener when the component unmounts
      linkingSubscription.remove();
    };
  },
};

/**
 * Utility function to create deep links
 * @param path - The path to create a deep link for
 * @param params - Optional parameters to include in the deep link
 * @returns The full deep link URL
 */
export const createDeepLink = (
  path: string,
  params?: Record<string, string | number>
): string => {
  let url = `${URL_SCHEME}${path}`;

  if (params && Object.keys(params).length > 0) {
    const queryParams = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
      .join('&');

    url += `?${queryParams}`;
  }

  return url;
};

/**
 * Parse parameters from a deep link URL
 * @param url - The deep link URL to parse
 * @returns An object containing the parsed parameters
 */
export const parseDeepLinkParams = (url: string): Record<string, string> => {
  const params: Record<string, string> = {};

  try {
    const parsedUrl = new URL(url);
    parsedUrl.searchParams.forEach((value, key) => {
      params[key] = value;
    });
  } catch (error) {
    console.error('Error parsing deep link URL:', error);
  }

  return params;
};

/**
 * Check if a URL is a valid deep link for this app
 * @param url - The URL to check
 * @returns Boolean indicating if the URL is a valid deep link
 */
export const isValidDeepLink = (url: string): boolean => {
  if (!url) return false;

  const prefixes = getPrefixes();
  return prefixes.some(prefix => url.startsWith(prefix));
};

export default linking;
