import React, { useEffect, useRef } from 'react';
import { Linking, AppState, AppStateStatus, Platform } from 'react-native';
import { useNavigation, NavigationContainerRef } from '@react-navigation/native';
import { isValidDeepLink, parseDeepLinkParams, DEEP_LINK_PATHS } from './linking';

/**
 * DeepLinkHandler component
 * 
 * This component handles deep links in the application by:
 * 1. Listening for deep links when the app is in the foreground
 * 2. Handling deep links when the app is opened from a background state
 * 3. Processing initial deep links when the app is launched
 * 
 * It should be placed near the root of your component tree, typically
 * inside the NavigationContainer component.
 */
const DeepLinkHandler: React.FC = () => {
  const navigation = useNavigation<NavigationContainerRef<ReactNavigation.RootParamList>>();
  const appState = useRef(AppState.currentState);
  const initialURL = useRef<string | null>(null);
  const hasHandledInitialURL = useRef(false);

  // Handle the deep link navigation based on the URL
  const handleDeepLink = (url: string) => {
    if (!url || !isValidDeepLink(url)) return;

    console.log('Processing deep link:', url);
    
    try {
      // Extract the path from the URL
      const urlObj = new URL(url);
      const path = urlObj.pathname.replace(/^\/+|\/+$/g, ''); // Remove leading/trailing slashes
      const params = parseDeepLinkParams(url);

      // Navigate based on the path
      switch (path) {
        case DEEP_LINK_PATHS.HOME:
          navigation.navigate('Home');
          break;

        case DEEP_LINK_PATHS.SERVICES:
          navigation.navigate('Services', { screen: 'ServicesList' });
          break;

        case DEEP_LINK_PATHS.SERVICE_DETAILS:
          if (params.serviceId) {
            navigation.navigate('Services', {
              screen: 'ServiceDetails',
              params: {
                serviceId: params.serviceId,
                serviceName: params.serviceName,
                fromDeepLink: true
              }
            });
          }
          break;

        case DEEP_LINK_PATHS.PROFILE:
          navigation.navigate('Profile');
          break;

        case DEEP_LINK_PATHS.APPOINTMENTS:
          navigation.navigate('Appointments', {
            screen: 'AppointmentsList',
            params: {
              filter: params.filter as 'upcoming' | 'past' | 'all' || 'all'
            }
          });
          break;

        case DEEP_LINK_PATHS.APPOINTMENT_DETAILS:
          if (params.appointmentId) {
            navigation.navigate('Appointments', {
              screen: 'AppointmentDetails',
              params: {
                appointmentId: params.appointmentId,
                fromNotification: params.fromNotification === 'true'
              }
            });
          }
          break;

        case DEEP_LINK_PATHS.NOTIFICATIONS:
          navigation.navigate('Notifications');
          break;

        case DEEP_LINK_PATHS.LOGIN:
          navigation.navigate('Login');
          break;

        case DEEP_LINK_PATHS.REGISTER:
          navigation.navigate('Register', {
            referralCode: params.referralCode
          });
          break;

        case DEEP_LINK_PATHS.FORGOT_PASSWORD:
          navigation.navigate('ForgotPassword');
          break;

        case DEEP_LINK_PATHS.EMERGENCY:
          navigation.navigate('Emergency', {
            immediate: params.immediate === 'true'
          });
          break;

        case DEEP_LINK_PATHS.SCHEDULE_VISIT:
          navigation.navigate('ScheduleVisit', {
            serviceType: params.serviceType,
            date: params.date
          });
          break;

        default:
          console.warn('Unhandled deep link path:', path);
          // Default to home screen if path is not recognized
          navigation.navigate('Home');
      }
    } catch (error) {
      console.error('Error handling deep link:', error);
    }
  };

  // Handle initial URL when app is first launched
  useEffect(() => {
    const getInitialURL = async () => {
      try {
        const url = await Linking.getInitialURL();
        if (url) {
          initialURL.current = url;
          if (!hasHandledInitialURL.current && navigation.isReady()) {
            handleDeepLink(url);
            hasHandledInitialURL.current = true;
          }
        }
      } catch (error) {
        console.error('Error getting initial URL:', error);
      }
    };

    getInitialURL();

    // Wait for navigation to be ready before handling the initial URL
    const unsubscribe = navigation.addListener('ready', () => {
      if (initialURL.current && !hasHandledInitialURL.current) {
        handleDeepLink(initialURL.current);
        hasHandledInitialURL.current = true;
      }
    });

    return unsubscribe;
  }, []);

  // Listen for deep links while the app is running
  useEffect(() => {
    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => subscription.remove();
  }, []);

  // Handle app state changes (background to foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        // App has come to the foreground
        Linking.getInitialURL().then(url => {
          if (url) {
            handleDeepLink(url);
          }
        });
      }
      
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default DeepLinkHandler;
