import { NavigatorScreenParams } from '@react-navigation/native';

/**
 * Type definitions for the React Navigation system in NurServNurseApp
 */

declare global {
  namespace ReactNavigation {
    /**
     * Root navigation parameter list
     * Defines all the main screens in the app and their parameters
     */
    interface RootParamList {
      // Main tab screens
      Home: undefined;
      Services: NavigatorScreenParams<ServicesParamList> | undefined;
      Profile: undefined;
      Appointments: NavigatorScreenParams<AppointmentsParamList> | undefined;
      Notifications: undefined;
      
      // Authentication screens
      Login: undefined;
      Register: { referralCode?: string };
      ForgotPassword: undefined;
      
      // Feature screens
      Emergency: { immediate?: boolean };
      ScheduleVisit: { serviceType?: string; date?: string };
    }
  }
}

/**
 * Parameter list for the Services navigator
 */
export type ServicesParamList = {
  ServicesList: undefined;
  ServiceDetails: {
    serviceId: string;
    serviceName?: string;
    fromDeepLink?: boolean;
  };
  ServiceBooking: {
    serviceId: string;
    preselectedDate?: string;
  };
};

/**
 * Parameter list for the Appointments navigator
 */
export type AppointmentsParamList = {
  AppointmentsList: { filter?: 'upcoming' | 'past' | 'all' };
  AppointmentDetails: {
    appointmentId: string;
    fromNotification?: boolean;
  };
  RescheduleAppointment: {
    appointmentId: string;
    currentDate?: string;
  };
};

/**
 * Type for navigation props for screens
 * Use this type for components that need access to navigation
 */
export type NavigationProps = {
  navigation: ReactNavigation.NavigationProp<ReactNavigation.RootParamList>;
  route: ReactNavigation.RouteProp<ReactNavigation.RootParamList, keyof ReactNavigation.RootParamList>;
};

/**
 * Helper type for screen-specific navigation props
 * @template T - The screen name from RootParamList
 */
export type ScreenNavigationProps<T extends keyof ReactNavigation.RootParamList> = {
  navigation: ReactNavigation.NavigationProp<ReactNavigation.RootParamList>;
  route: ReactNavigation.RouteProp<ReactNavigation.RootParamList, T>;
};

/**
 * Helper type for nested navigator screen props
 * @template T - The parent navigator name from RootParamList
 * @template S - The screen name from the nested navigator's param list
 */
export type NestedScreenProps<
  T extends keyof ReactNavigation.RootParamList,
  N extends Record<string, any>,
  S extends keyof N
> = {
  navigation: ReactNavigation.NavigationProp<ReactNavigation.RootParamList>;
  route: ReactNavigation.RouteProp<N, S> & { params: N[S] };
};

// Export specific screen prop types for convenience
export type HomeScreenProps = ScreenNavigationProps<'Home'>;
export type ServiceDetailsScreenProps = NestedScreenProps<'Services', ServicesParamList, 'ServiceDetails'>;
export type AppointmentDetailsScreenProps = NestedScreenProps<'Appointments', AppointmentsParamList, 'AppointmentDetails'>;
