import { Linking } from 'react-native';
import {
  createDeepLink,
  parseDeepLinkParams,
  isValidDeepLink,
  URL_SCHEME,
  DEEP_LINK_PATHS,
} from '../src/utils/linking';

// Mock React Native Linking
jest.mock('react-native', () => ({
  Linking: {
    getInitialURL: jest.fn(),
    addEventListener: jest.fn(),
  },
}));

describe('Linking Utils', () => {
  describe('createDeepLink', () => {
    it('should create a basic deep link without parameters', () => {
      const result = createDeepLink(DEEP_LINK_PATHS.HOME);
      expect(result).toBe(`${URL_SCHEME}${DEEP_LINK_PATHS.HOME}`);
    });

    it('should create a deep link with parameters', () => {
      const params = { serviceId: '123', serviceName: 'Test Service' };
      const result = createDeepLink(DEEP_LINK_PATHS.SERVICE_DETAILS, params);
      expect(result).toBe(`${URL_SCHEME}${DEEP_LINK_PATHS.SERVICE_DETAILS}?serviceId=123&serviceName=Test%20Service`);
    });

    it('should handle empty parameters object', () => {
      const result = createDeepLink(DEEP_LINK_PATHS.PROFILE, {});
      expect(result).toBe(`${URL_SCHEME}${DEEP_LINK_PATHS.PROFILE}`);
    });
  });

  describe('parseDeepLinkParams', () => {
    it('should parse parameters from a deep link URL', () => {
      const url = `${URL_SCHEME}services/123?serviceId=123&serviceName=Test%20Service`;
      const result = parseDeepLinkParams(url);
      expect(result).toEqual({
        serviceId: '123',
        serviceName: 'Test Service',
      });
    });

    it('should return empty object for URL without parameters', () => {
      const url = `${URL_SCHEME}home`;
      const result = parseDeepLinkParams(url);
      expect(result).toEqual({});
    });

    it('should handle invalid URLs gracefully', () => {
      const result = parseDeepLinkParams('invalid-url');
      expect(result).toEqual({});
    });
  });

  describe('isValidDeepLink', () => {
    it('should return true for valid app scheme URLs', () => {
      const url = `${URL_SCHEME}home`;
      expect(isValidDeepLink(url)).toBe(true);
    });

    it('should return true for valid web URLs', () => {
      const url = 'https://dev.nurservnurse.com/services';
      expect(isValidDeepLink(url)).toBe(true);
    });

    it('should return false for invalid URLs', () => {
      expect(isValidDeepLink('https://example.com')).toBe(false);
      expect(isValidDeepLink('invalid-url')).toBe(false);
      expect(isValidDeepLink('')).toBe(false);
    });

    it('should return false for null or undefined URLs', () => {
      expect(isValidDeepLink(null as any)).toBe(false);
      expect(isValidDeepLink(undefined as any)).toBe(false);
    });
  });
});

describe('React Native Linking Integration', () => {
  it('should use React Native Linking instead of Expo', () => {
    // Verify that we're using React Native's Linking API
    expect(Linking).toBeDefined();
    expect(Linking.getInitialURL).toBeDefined();
    expect(Linking.addEventListener).toBeDefined();
  });
});
