/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.Dynamic;
import com.facebook.react.bridge.ReadableMap;

public interface RNSVGSvgViewAndroidManagerInterface<T extends View> {
  void setBbWidth(T view, Dynamic value);
  void setBbHeight(T view, Dynamic value);
  void setMinX(T view, float value);
  void setMinY(T view, float value);
  void setVbWidth(T view, float value);
  void setVbHeight(T view, float value);
  void setAlign(T view, @Nullable String value);
  void setMeetOrSlice(T view, int value);
  void setColor(T view, @Nullable Integer value);
  void setPointerEvents(T view, @Nullable String value);
  void setHasTVPreferredFocus(T view, boolean value);
  void setBorderBottomColor(T view, @Nullable Integer value);
  void setNextFocusDown(T view, int value);
  void setBorderRightColor(T view, @Nullable Integer value);
  void setNextFocusRight(T view, int value);
  void setBorderLeftColor(T view, @Nullable Integer value);
  void setBorderColor(T view, @Nullable Integer value);
  void setRemoveClippedSubviews(T view, boolean value);
  void setNextFocusForward(T view, int value);
  void setNextFocusUp(T view, int value);
  void setAccessible(T view, boolean value);
  void setBorderStartColor(T view, @Nullable Integer value);
  void setBorderEndColor(T view, @Nullable Integer value);
  void setFocusable(T view, boolean value);
  void setNativeBackgroundAndroid(T view, @Nullable ReadableMap value);
  void setNativeForegroundAndroid(T view, @Nullable ReadableMap value);
  void setBackfaceVisibility(T view, @Nullable String value);
  void setBorderStyle(T view, @Nullable String value);
  void setNeedsOffscreenAlphaCompositing(T view, boolean value);
  void setHitSlop(T view, Dynamic value);
  void setBorderTopColor(T view, @Nullable Integer value);
  void setNextFocusLeft(T view, int value);
  void setBorderBlockColor(T view, @Nullable Integer value);
  void setBorderBlockEndColor(T view, @Nullable Integer value);
  void setBorderBlockStartColor(T view, @Nullable Integer value);
  void setBorderRadius(T view, Dynamic value);
  void setBorderTopLeftRadius(T view, Dynamic value);
  void setBorderTopRightRadius(T view, Dynamic value);
  void setBorderBottomRightRadius(T view, Dynamic value);
  void setBorderBottomLeftRadius(T view, Dynamic value);
  void setBorderTopStartRadius(T view, Dynamic value);
  void setBorderTopEndRadius(T view, Dynamic value);
  void setBorderBottomStartRadius(T view, Dynamic value);
  void setBorderBottomEndRadius(T view, Dynamic value);
  void setBorderEndEndRadius(T view, Dynamic value);
  void setBorderEndStartRadius(T view, Dynamic value);
  void setBorderStartEndRadius(T view, Dynamic value);
  void setBorderStartStartRadius(T view, Dynamic value);
}
