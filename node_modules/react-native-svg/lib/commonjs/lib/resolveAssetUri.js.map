{"version": 3, "names": ["_reactNative", "require", "_registry", "svgDataUriPattern", "resolveAssetUri", "source", "src", "asset", "getAssetByID", "Error", "width", "height", "scale", "scales", "length", "preferredScale", "PixelRatio", "get", "reduce", "prev", "curr", "Math", "abs", "scaleSuffix", "uri", "httpServerLocation", "name", "type", "Array", "isArray", "_src", "match", "prefix", "svg", "encodedSvg", "encodeURIComponent"], "sourceRoot": "../../../src", "sources": ["lib/resolveAssetUri.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAMA,IAAAC,SAAA,GAAAD,OAAA;AADA;;AAeA,MAAME,iBAAiB,GAAG,mCAAmC;;AAE7D;AACO,SAASC,eAAeA,CAC7BC,MAAiD,EACP;EAC1C,IAAIC,GAAsC,GAAG,CAAC,CAAC;EAC/C,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;IAC9B;IACA,MAAME,KAA2B,GAAG,IAAAC,sBAAY,EAACH,MAAM,CAAC;IACxD,IAAIE,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIE,KAAK,CACb,yBAAyBJ,MAAM,kEACjC,CAAC;IACH;IACAC,GAAG,GAAG;MACJI,KAAK,EAAEH,KAAK,CAACG,KAAK;MAClBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;MACpBC,KAAK,EAAEL,KAAK,CAACM,MAAM,CAAC,CAAC;IACvB,CAAC;IACD,IAAIN,KAAK,CAACM,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,cAAc,GAAGC,uBAAU,CAACC,GAAG,CAAC,CAAC;MACvC;MACAX,GAAG,CAACM,KAAK,GAAGL,KAAK,CAACM,MAAM,CAACK,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KACzCC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAGL,cAAc,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACH,IAAI,GAAGJ,cAAc,CAAC,GAC7DK,IAAI,GACJD,IACN,CAAC;IACH;IACA,MAAMI,WAAW,GAAGjB,GAAG,CAACM,KAAK,KAAK,CAAC,GAAG,IAAIN,GAAG,CAACM,KAAK,GAAG,GAAG,EAAE;IAC3DN,GAAG,CAACkB,GAAG,GAAGjB,KAAK,GACX,GAAGA,KAAK,CAACkB,kBAAkB,IAAIlB,KAAK,CAACmB,IAAI,GAAGH,WAAW,IAAIhB,KAAK,CAACoB,IAAI,EAAE,GACvE,EAAE;EACR,CAAC,MAAM,IAAI,OAAOtB,MAAM,KAAK,QAAQ,EAAE;IACrCC,GAAG,CAACkB,GAAG,GAAGnB,MAAM;EAClB,CAAC,MAAM,IACLA,MAAM,IACN,CAACuB,KAAK,CAACC,OAAO,CAACxB,MAAM,CAAC,IACtB,OAAOA,MAAM,CAACmB,GAAG,KAAK,QAAQ,EAC9B;IACAlB,GAAG,CAACkB,GAAG,GAAGnB,MAAM,CAACmB,GAAG;EACtB;EAEA,IAAIlB,GAAG,CAACkB,GAAG,EAAE;IAAA,IAAAM,IAAA;IACX,MAAMC,KAAK,IAAAD,IAAA,GAAGxB,GAAG,cAAAwB,IAAA,gBAAAA,IAAA,GAAHA,IAAA,CAAKN,GAAG,cAAAM,IAAA,uBAARA,IAAA,CAAUC,KAAK,CAAC5B,iBAAiB,CAAC;IAChD;IACA,IAAI4B,KAAK,EAAE;MACT,MAAM,GAAGC,MAAM,EAAEC,GAAG,CAAC,GAAGF,KAAK;MAC7B,MAAMG,UAAU,GAAGC,kBAAkB,CAACF,GAAG,CAAC;MAC1C3B,GAAG,CAACkB,GAAG,GAAG,GAAGQ,MAAM,GAAGE,UAAU,EAAE;MAClC,OAAO5B,GAAG;IACZ;EACF;EACA,OAAOA,GAAG;AACZ", "ignoreList": []}