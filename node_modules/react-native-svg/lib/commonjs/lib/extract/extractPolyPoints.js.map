{"version": 3, "names": ["extractPolyPoints", "points", "polyPoints", "Array", "isArray", "join", "replace", "split"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractPolyPoints.ts"], "mappings": ";;;;;;AAEe,SAASA,iBAAiBA,CACvCC,MAAsC,EACtC;EACA,MAAMC,UAAU,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,IAAI,CAAC,GAAG,CAAC,GAAGJ,MAAM;EACpE,OAAQC,UAAU,CACfI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CACvBC,KAAK,CAAC,kBAAkB,CAAC,CACzBF,IAAI,CAAC,GAAG,CAAC;AACd", "ignoreList": []}