{"version": 3, "names": ["_extractBrush", "_interopRequireDefault", "require", "_extractOpacity", "_reactNative", "e", "__esModule", "default", "fillRules", "evenodd", "nonzero", "defaultFill", "type", "payload", "processColor", "extractFill", "o", "props", "inherited", "fill", "fillRule", "fillOpacity", "push", "extractBrush", "extractOpacity"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractFill.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,YAAA,GAAAF,OAAA;AAA4C,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE5C,MAAMG,SAA+C,GAAG;EACtDC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,WAAW,GAAG;EAAEC,IAAI,EAAE,CAAC;EAAEC,OAAO,EAAE,IAAAC,yBAAY,EAAC,OAAO;AAAE,CAAC;AAEhD,SAASC,WAAWA,CACjCC,CAAiB,EACjBC,KAAgB,EAChBC,SAAmB,EACnB;EACA,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC;EAAY,CAAC,GAAGJ,KAAK;EAC7C,IAAIE,IAAI,IAAI,IAAI,EAAE;IAChBD,SAAS,CAACI,IAAI,CAAC,MAAM,CAAC;IACtBN,CAAC,CAACG,IAAI,GACJ,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGR,WAAW,GAAG,IAAAY,qBAAY,EAACJ,IAAI,CAAC;EACxE,CAAC,MAAM;IACL;IACAH,CAAC,CAACG,IAAI,GAAGR,WAAW;EACtB;EACA,IAAIU,WAAW,IAAI,IAAI,EAAE;IACvBH,SAAS,CAACI,IAAI,CAAC,aAAa,CAAC;IAC7BN,CAAC,CAACK,WAAW,GAAG,IAAAG,uBAAc,EAACH,WAAW,CAAC;EAC7C;EACA,IAAID,QAAQ,IAAI,IAAI,EAAE;IACpBF,SAAS,CAACI,IAAI,CAAC,UAAU,CAAC;IAC1BN,CAAC,CAACI,QAAQ,GAAGA,QAAQ,IAAIZ,SAAS,CAACY,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;EAC5D;AACF", "ignoreList": []}