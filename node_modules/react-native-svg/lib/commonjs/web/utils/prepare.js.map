{"version": 3, "names": ["_hasProperty", "require", "_parseTransform", "_resolve", "_resolveAssetUri2", "prepare", "self", "props", "transform", "origin", "originX", "originY", "fontFamily", "fontSize", "fontWeight", "fontStyle", "style", "forwardedRef", "gradientTransform", "patternTransform", "onPress", "rest", "clean", "hasTouchableProperty", "onStartShouldSetResponder", "touchableHandleStartShouldSetResponder", "onResponderTerminationRequest", "touchableHandleResponderTerminationRequest", "onResponderGrant", "touchableHandleResponderGrant", "onResponderMove", "touchableHandleResponderMove", "onResponderRelease", "touchableHandleResponderRelease", "onResponderTerminate", "touchableHandleResponderTerminate", "toString", "replace", "parsedTransform", "parseTransformProp", "parsedGradientTransform", "parsedPatternTransform", "ref", "el", "elementRef", "current", "styles", "resolve", "onClick", "href", "undefined", "_resolveAssetUri", "resolveAssetUri", "uri", "exports"], "sourceRoot": "../../../../src", "sources": ["web/utils/prepare.ts"], "mappings": ";;;;;;AAMA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,IAAAG,iBAAA,GAAAH,OAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMI,OAAO,GAAGA,CACrBC,IAAiB,EACjBC,KAAK,GAAGD,IAAI,CAACC,KAAK,KACf;EACH,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,KAAK;IACLC,YAAY;IACZC,iBAAiB;IACjBC,gBAAgB;IAChBC,OAAO;IACP,GAAGC;EACL,CAAC,GAAGd,KAAK;EAET,MAAMe,KAeL,GAAG;IACF,IAAI,IAAAC,iCAAoB,EAAChB,KAAK,CAAC,GAC3B;MACEiB,yBAAyB,EACvBlB,IAAI,CAACmB,sCAAsC;MAC7CC,6BAA6B,EAC3BpB,IAAI,CAACqB,0CAA0C;MACjDC,gBAAgB,EAAEtB,IAAI,CAACuB,6BAA6B;MACpDC,eAAe,EAAExB,IAAI,CAACyB,4BAA4B;MAClDC,kBAAkB,EAAE1B,IAAI,CAAC2B,+BAA+B;MACxDC,oBAAoB,EAAE5B,IAAI,CAAC6B;IAC7B,CAAC,GACD,IAAI,CAAC;IACT,GAAGd;EACL,CAAC;EAED,IAAIZ,MAAM,IAAI,IAAI,EAAE;IAClBa,KAAK,CAAC,kBAAkB,CAAC,GAAGb,MAAM,CAAC2B,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjE,CAAC,MAAM,IAAI3B,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;IAC7CW,KAAK,CAAC,kBAAkB,CAAC,GAAG,GAAGZ,OAAO,IAAI,CAAC,IAAIC,OAAO,IAAI,CAAC,EAAE;EAC/D;;EAEA;EACA,MAAM2B,eAAe,GAAG,IAAAC,kCAAkB,EAAC/B,SAAS,EAAED,KAAK,CAAC;EAC5D,IAAI+B,eAAe,EAAE;IACnBhB,KAAK,CAACd,SAAS,GAAG8B,eAAe;EACnC;EACA,MAAME,uBAAuB,GAAG,IAAAD,kCAAkB,EAACrB,iBAAiB,CAAC;EACrE,IAAIsB,uBAAuB,EAAE;IAC3BlB,KAAK,CAACJ,iBAAiB,GAAGsB,uBAAuB;EACnD;EACA,MAAMC,sBAAsB,GAAG,IAAAF,kCAAkB,EAACpB,gBAAgB,CAAC;EACnE,IAAIsB,sBAAsB,EAAE;IAC1BnB,KAAK,CAACH,gBAAgB,GAAGsB,sBAAsB;EACjD;EAEAnB,KAAK,CAACoB,GAAG,GAAIC,EAAqB,IAAK;IACrCrC,IAAI,CAACsC,UAAU,CAACC,OAAO,GAAGF,EAAE;IAC5B,IAAI,OAAO1B,YAAY,KAAK,UAAU,EAAE;MACtCA,YAAY,CAAC0B,EAAE,CAAC;IAClB,CAAC,MAAM,IAAI1B,YAAY,EAAE;MACvBA,YAAY,CAAC4B,OAAO,GAAGF,EAAE;IAC3B;EACF,CAAC;EAED,MAAMG,MAKL,GAAG,CAAC,CAAC;EAEN,IAAIlC,UAAU,IAAI,IAAI,EAAE;IACtBkC,MAAM,CAAClC,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpBiC,MAAM,CAACjC,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtBgC,MAAM,CAAChC,UAAU,GAAGA,UAAU;EAChC;EACA,IAAIC,SAAS,IAAI,IAAI,EAAE;IACrB+B,MAAM,CAAC/B,SAAS,GAAGA,SAAS;EAC9B;EACAO,KAAK,CAACN,KAAK,GAAG,IAAA+B,gBAAO,EAAC/B,KAAK,EAAE8B,MAAM,CAAC;EACpC,IAAI1B,OAAO,KAAK,IAAI,EAAE;IACpBE,KAAK,CAAC0B,OAAO,GAAGzC,KAAK,CAACa,OAAO;EAC/B;EACA,IAAIb,KAAK,CAAC0C,IAAI,KAAK,IAAI,IAAI1C,KAAK,CAAC0C,IAAI,KAAKC,SAAS,EAAE;IAAA,IAAAC,gBAAA;IACnD7B,KAAK,CAAC2B,IAAI,IAAAE,gBAAA,GAAG,IAAAC,iCAAe,EAAC7C,KAAK,CAAC0C,IAAI,CAAC,cAAAE,gBAAA,uBAA3BA,gBAAA,CAA6BE,GAAG;EAC/C;EACA,OAAO/B,KAAK;AACd,CAAC;AAACgC,OAAA,CAAAjD,OAAA,GAAAA,OAAA", "ignoreList": []}