"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _codegenNativeComponent = _interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// TODO: import ImageSource from codegen types when it is available
var _default = exports.default = (0, _codegenNativeComponent.default)('RNSVGImage', {
  interfaceOnly: true
});
//# sourceMappingURL=ImageNativeComponent.js.map