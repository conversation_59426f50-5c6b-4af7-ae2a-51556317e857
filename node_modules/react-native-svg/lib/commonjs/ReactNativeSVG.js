"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  Shape: true,
  camelCase: true,
  parse: true,
  SvgAst: true,
  SvgFromUri: true,
  SvgFromXml: true,
  SvgUri: true,
  SvgXml: true,
  fetchText: true,
  RNSVGCircle: true,
  RNSVGClipPath: true,
  RNSVGDefs: true,
  RNSVGEllipse: true,
  RNSVGFeColorMatrix: true,
  RNSVGFeComposite: true,
  RNSVGFeGaussianBlur: true,
  RNSVGFeMerge: true,
  RNSVGFeOffset: true,
  RNSVGFilter: true,
  RNSVGForeignObject: true,
  RNSVGGroup: true,
  RNSVGImage: true,
  RNSVGLine: true,
  RNSVGLinearGradient: true,
  RNSVGMarker: true,
  RNSVGMask: true,
  RNSVGPath: true,
  RNSVGPattern: true,
  RNSVGRadialGradient: true,
  RNSVGRect: true,
  RNSVGSvgAndroid: true,
  RNSVGSvgIOS: true,
  RNSVGSymbol: true,
  RNSVGText: true,
  RNSVGTextPath: true,
  RNSVGTSpan: true,
  RNSVGUse: true,
  inlineStyles: true,
  loadLocalRawResource: true,
  LocalSvg: true,
  SvgCss: true,
  SvgCssUri: true,
  SvgWithCss: true,
  SvgWithCssUri: true,
  WithLocalSvg: true
};
Object.defineProperty(exports, "LocalSvg", {
  enumerable: true,
  get: function () {
    return _deprecated.LocalSvg;
  }
});
Object.defineProperty(exports, "RNSVGCircle", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGCircle;
  }
});
Object.defineProperty(exports, "RNSVGClipPath", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGClipPath;
  }
});
Object.defineProperty(exports, "RNSVGDefs", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGDefs;
  }
});
Object.defineProperty(exports, "RNSVGEllipse", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGEllipse;
  }
});
Object.defineProperty(exports, "RNSVGFeColorMatrix", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFeColorMatrix;
  }
});
Object.defineProperty(exports, "RNSVGFeComposite", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFeComposite;
  }
});
Object.defineProperty(exports, "RNSVGFeGaussianBlur", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFeGaussianBlur;
  }
});
Object.defineProperty(exports, "RNSVGFeMerge", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFeMerge;
  }
});
Object.defineProperty(exports, "RNSVGFeOffset", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFeOffset;
  }
});
Object.defineProperty(exports, "RNSVGFilter", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGFilter;
  }
});
Object.defineProperty(exports, "RNSVGForeignObject", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGForeignObject;
  }
});
Object.defineProperty(exports, "RNSVGGroup", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGGroup;
  }
});
Object.defineProperty(exports, "RNSVGImage", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGImage;
  }
});
Object.defineProperty(exports, "RNSVGLine", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGLine;
  }
});
Object.defineProperty(exports, "RNSVGLinearGradient", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGLinearGradient;
  }
});
Object.defineProperty(exports, "RNSVGMarker", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGMarker;
  }
});
Object.defineProperty(exports, "RNSVGMask", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGMask;
  }
});
Object.defineProperty(exports, "RNSVGPath", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGPath;
  }
});
Object.defineProperty(exports, "RNSVGPattern", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGPattern;
  }
});
Object.defineProperty(exports, "RNSVGRadialGradient", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGRadialGradient;
  }
});
Object.defineProperty(exports, "RNSVGRect", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGRect;
  }
});
Object.defineProperty(exports, "RNSVGSvgAndroid", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGSvgAndroid;
  }
});
Object.defineProperty(exports, "RNSVGSvgIOS", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGSvgIOS;
  }
});
Object.defineProperty(exports, "RNSVGSymbol", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGSymbol;
  }
});
Object.defineProperty(exports, "RNSVGTSpan", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGTSpan;
  }
});
Object.defineProperty(exports, "RNSVGText", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGText;
  }
});
Object.defineProperty(exports, "RNSVGTextPath", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGTextPath;
  }
});
Object.defineProperty(exports, "RNSVGUse", {
  enumerable: true,
  get: function () {
    return _fabric.RNSVGUse;
  }
});
Object.defineProperty(exports, "Shape", {
  enumerable: true,
  get: function () {
    return _Shape.default;
  }
});
Object.defineProperty(exports, "SvgAst", {
  enumerable: true,
  get: function () {
    return _xml.SvgAst;
  }
});
Object.defineProperty(exports, "SvgCss", {
  enumerable: true,
  get: function () {
    return _deprecated.SvgCss;
  }
});
Object.defineProperty(exports, "SvgCssUri", {
  enumerable: true,
  get: function () {
    return _deprecated.SvgCssUri;
  }
});
Object.defineProperty(exports, "SvgFromUri", {
  enumerable: true,
  get: function () {
    return _xml.SvgFromUri;
  }
});
Object.defineProperty(exports, "SvgFromXml", {
  enumerable: true,
  get: function () {
    return _xml.SvgFromXml;
  }
});
Object.defineProperty(exports, "SvgUri", {
  enumerable: true,
  get: function () {
    return _xml.SvgUri;
  }
});
Object.defineProperty(exports, "SvgWithCss", {
  enumerable: true,
  get: function () {
    return _deprecated.SvgWithCss;
  }
});
Object.defineProperty(exports, "SvgWithCssUri", {
  enumerable: true,
  get: function () {
    return _deprecated.SvgWithCssUri;
  }
});
Object.defineProperty(exports, "SvgXml", {
  enumerable: true,
  get: function () {
    return _xml.SvgXml;
  }
});
Object.defineProperty(exports, "WithLocalSvg", {
  enumerable: true,
  get: function () {
    return _deprecated.WithLocalSvg;
  }
});
Object.defineProperty(exports, "camelCase", {
  enumerable: true,
  get: function () {
    return _xml.camelCase;
  }
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _elements.default;
  }
});
Object.defineProperty(exports, "fetchText", {
  enumerable: true,
  get: function () {
    return _fetchData.fetchText;
  }
});
Object.defineProperty(exports, "inlineStyles", {
  enumerable: true,
  get: function () {
    return _deprecated.inlineStyles;
  }
});
Object.defineProperty(exports, "loadLocalRawResource", {
  enumerable: true,
  get: function () {
    return _deprecated.loadLocalRawResource;
  }
});
Object.defineProperty(exports, "parse", {
  enumerable: true,
  get: function () {
    return _xml.parse;
  }
});
var _Shape = _interopRequireDefault(require("./elements/Shape"));
var _xml = require("./xml");
var _fetchData = require("./utils/fetchData");
var _fabric = require("./fabric");
var _deprecated = require("./deprecated");
var _types = require("./lib/extract/types");
Object.keys(_types).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _types[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _types[key];
    }
  });
});
var _elements = _interopRequireWildcard(require("./elements"));
Object.keys(_elements).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _elements[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _elements[key];
    }
  });
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
//# sourceMappingURL=ReactNativeSVG.js.map