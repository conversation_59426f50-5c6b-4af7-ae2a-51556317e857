{"version": 3, "names": ["_react", "require", "_util", "FeDistantLight", "Component", "displayName", "defaultProps", "render", "warnUnimplementedFilter", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FeDistantLight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAOe,MAAME,cAAc,SAASC,gBAAS,CAAsB;EACzE,OAAOC,WAAW,GAAG,gBAAgB;EAErC,OAAOC,YAAY,GAAG,CAAC,CAAC;EAExBC,MAAMA,CAAA,EAAG;IACP,IAAAC,6BAAuB,EAAC,CAAC;IACzB,OAAO,IAAI;EACb;AACF;AAACC,OAAA,CAAAC,OAAA,GAAAP,cAAA", "ignoreList": []}