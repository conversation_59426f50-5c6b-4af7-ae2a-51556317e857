{"version": 3, "names": ["Component", "FilterPrimitive", "root", "defaultPrimitiveProps", "refMethod", "instance", "setNativeProps", "props", "_this$root"], "sourceRoot": "../../../../src", "sources": ["elements/filters/FilterPrimitive.tsx"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AAYjC,eAAe,MAAMC,eAAe,SAAYD,SAAS,CAEvD;EAEAE,IAAI,GAAgD,IAAI;EAExD,OAAOC,qBAAqB,GAC1B,CAAC,CAAC;EAEJC,SAAS,GACPC,QAAqD,IAClD;IACH,IAAI,CAACH,IAAI,GAAGG,QAAQ;EACtB,CAAC;EAEDC,cAAc,GAAIC,KAAQ,IAAK;IAAA,IAAAC,UAAA;IAC7B,CAAAA,UAAA,OAAI,CAACN,IAAI,cAAAM,UAAA,eAATA,UAAA,CAAWF,cAAc,CAACC,KAAK,CAAC;EAClC,CAAC;AACH", "ignoreList": []}