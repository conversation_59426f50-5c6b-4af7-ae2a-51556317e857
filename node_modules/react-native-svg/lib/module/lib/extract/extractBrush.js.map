{"version": 3, "names": ["processColor", "convertPercentageColor", "urlIdPattern", "currentColorBrush", "type", "contextFillBrush", "contextStrokeBrush", "extractBrush", "color", "brush", "match", "brushRef", "colorToProcess", "processedColor", "payload", "console", "warn", "String"], "sourceRoot": "../../../../src", "sources": ["lib/extract/extractBrush.ts"], "mappings": "AACA,SAASA,YAAY,QAAQ,cAAc;AAC3C,SAASC,sBAAsB,QAAQ,iCAAiC;AAExE,MAAMC,YAAY,GAAG,gBAAgB;AAErC,MAAMC,iBAAiB,GAAG;EAAEC,IAAI,EAAE;AAAE,CAAC;AACrC,MAAMC,gBAAgB,GAAG;EAAED,IAAI,EAAE;AAAE,CAAC;AACpC,MAAME,kBAAkB,GAAG;EAAEF,IAAI,EAAE;AAAE,CAAC;AAEtC,eAAe,SAASG,YAAYA,CAACC,KAAiB,EAAE;EACtD,IAAIA,KAAK,KAAK,MAAM,EAAE;IACpB,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,KAAK,cAAc,EAAE;IAC5B,OAAOL,iBAAiB;EAC1B;EAEA,IAAIK,KAAK,KAAK,cAAc,EAAE;IAC5B,OAAOH,gBAAgB;EACzB;EAEA,IAAIG,KAAK,KAAK,gBAAgB,EAAE;IAC9B,OAAOF,kBAAkB;EAC3B;EAEA,MAAMG,KAAK,GAAG,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,KAAK,CAACR,YAAY,CAAC;EACpE,IAAIO,KAAK,EAAE;IACT,OAAO;MAAEL,IAAI,EAAE,CAAC;MAAEO,QAAQ,EAAEF,KAAK,CAAC,CAAC;IAAE,CAAC;EACxC;;EAEA;EACA,MAAMG,cAAc,GAAGX,sBAAsB,CAACO,KAAK,CAAC;EACpD,MAAMK,cAAc,GAAGb,YAAY,CAACY,cAAc,CAAC;EACnD,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;IACtC,OAAO;MAAET,IAAI,EAAE,CAAC;MAAEU,OAAO,EAAED;IAAe,CAAC;EAC7C;EAEA,IAAI,OAAOA,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,IAAI,EAAE;IACjE;IACA;IACA;IACA,OAAO;MAAET,IAAI,EAAE,CAAC;MAAEU,OAAO,EAAED;IAAe,CAAC;EAC7C;EAEAE,OAAO,CAACC,IAAI,CAAC,IAAIC,MAAM,CAACT,KAAK,CAAC,iCAAiC,CAAC;EAChE,OAAO,IAAI;AACb", "ignoreList": []}