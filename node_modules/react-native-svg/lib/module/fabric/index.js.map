{"version": 3, "names": ["RNSVGCircle", "RNSVGClipPath", "RNSVGDefs", "RNSVGEllipse", "RNSVGForeignObject", "RNSVGGroup", "RNSVGImage", "RNSVGLinearGradient", "RNSVGLine", "RNSVGMarker", "RNSVGMask", "RNSVGPath", "RNSVGPattern", "RNSVGRadialGradient", "RNSVGRect", "RNSVGSvgAndroid", "RNSVGSvgIOS", "RNSVGSymbol", "RNSVGText", "RNSVGTextPath", "RNSVGTSpan", "RNSVGUse", "RNSVGFilter", "RNSVGFeBlend", "RNSVGFeColorMatrix", "RNSVGFeComposite", "RNSVGFeFlood", "RNSVGFeGaussianBlur", "RNSVGFeMerge", "RNSVGFeOffset"], "sourceRoot": "../../../src", "sources": ["fabric/index.ts"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,aAAa,MAAM,2BAA2B;AAErD,SACE7B,WAAW,EACXC,aAAa,EACbC,SAAS,EACTC,YAAY,EACZC,kBAAkB,EAClBC,UAAU,EACVC,UAAU,EACVC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,mBAAmB,EACnBC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,YAAY,EACZC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,EACZC,aAAa", "ignoreList": []}