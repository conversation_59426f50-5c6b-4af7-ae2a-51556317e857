{"name": "@react-native/metro-config", "version": "0.80.1", "description": "Metro configuration for React Native.", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/metro-config"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/metro-config#readme", "keywords": ["metro", "config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@react-native/js-polyfills": "0.80.1", "@react-native/metro-babel-transformer": "0.80.1", "metro-config": "^0.82.2", "metro-runtime": "^0.82.2"}}