{"name": "@react-native/metro-babel-transformer", "version": "0.80.1", "description": "Babel transformer for React Native applications.", "repository": {"type": "git", "url": "git+ssh://**************/facebook/react-native.git", "directory": "packages/react-native-babel-transformer"}, "keywords": ["transformer", "react-native", "metro"], "license": "MIT", "engines": {"node": ">=18"}, "main": "src/index.js", "files": ["src", "README.md", "!**/__docs__/**", "!**/__fixtures__/**", "!**/__mocks__/**", "!**/__tests__/**"], "dependencies": {"@babel/core": "^7.25.2", "@react-native/babel-preset": "0.80.1", "hermes-parser": "0.28.1", "nullthrows": "^1.1.1"}, "peerDependencies": {"@babel/core": "*"}}