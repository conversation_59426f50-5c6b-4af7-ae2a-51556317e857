/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

const Links: Array<{
  title: string,
  description: string,
  url: string,
}> = [
  {
    title: 'Hello World',
    description: 'Learn the basics',
    url: 'https://reactnative.dev/docs/tutorial',
  },
  {
    title: 'Fast Refresh',
    description: 'See edits instantly',
    url: 'https://reactnative.dev/docs/fast-refresh',
  },
  {
    title: 'DevTools',
    description: 'View logs & debug your app',
    url: 'https://reactnative.dev/docs/debugging',
  },
  {
    title: 'Components',
    description: 'View components and APIs',
    url: 'https://reactnative.dev/docs/components-and-apis',
  },
  {
    title: 'Style',
    description: 'Use the style prop',
    url: 'https://reactnative.dev/docs/style',
  },
  {
    title: 'Layout',
    description: 'Flexbox & layout techniques',
    url: 'https://reactnative.dev/docs/flexbox',
  },
  {
    title: 'Navigation',
    description: 'Move between screens',
    url: 'https://reactnative.dev/docs/navigation',
  },
  {
    title: 'Networking',
    description: 'Use the Fetch API',
    url: 'https://reactnative.dev/docs/network',
  },
  {
    title: 'Showcase',
    description: 'Featured React Native apps',
    url: 'https://reactnative.dev/showcase',
  },
  {
    title: 'Blog',
    description: 'Latest news & updates',
    url: 'https://reactnative.dev/blog',
  },
  {
    title: 'Community',
    description: 'Expore & get help',
    url: 'https://reactnative.dev/community/overview',
  },
  {
    title: 'Follow @reactnative',
    description: 'Stay in touch on X',
    url: 'https://x.com/reactnative',
  },
];

export default Links;
