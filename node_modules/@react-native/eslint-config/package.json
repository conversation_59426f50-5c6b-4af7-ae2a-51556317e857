{"name": "@react-native/eslint-config", "version": "0.80.1", "description": "ESLint config for React Native", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/eslint-config-react-native"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/eslint-config-react-native#readme", "keywords": ["eslint", "config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "main": "index.js", "dependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@react-native/eslint-plugin": "0.80.1", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^4.0.0"}, "peerDependencies": {"eslint": ">=8", "prettier": ">=2"}, "devDependencies": {"eslint": "^8.57.0", "prettier": "2.8.8"}}